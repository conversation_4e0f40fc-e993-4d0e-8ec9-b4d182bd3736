package com.step2energy.app.repository

import androidx.lifecycle.LiveData
import com.step2energy.app.data.UserDao
import com.step2energy.app.model.User

class UserRepository(private val userDao: UserDao) {
    
    val user: LiveData<User?> = userDao.getUser()
    
    suspend fun insert(user: User) {
        userDao.insert(user)
    }
    
    suspend fun update(user: User) {
        userDao.update(user)
    }
    
    suspend fun delete(user: User) {
        userDao.delete(user)
    }
    
    suspend fun deleteAll() {
        userDao.deleteAll()
    }
}
