<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/onboardingFragment">

    <fragment
        android:id="@+id/onboardingFragment"
        android:name="com.step2energy.app.ui.OnboardingFragment"
        android:label="Onboarding"
        tools:layout="@layout/fragment_onboarding">
        <action
            android:id="@+id/action_onboardingFragment_to_homeFragment"
            app:destination="@id/homeFragment"
            app:popUpTo="@id/onboardingFragment"
            app:popUpToInclusive="true" />
    </fragment>

    <fragment
        android:id="@+id/homeFragment"
        android:name="com.step2energy.app.ui.HomeFragment"
        android:label="Home"
        tools:layout="@layout/fragment_home" />

    <fragment
        android:id="@+id/energyFragment"
        android:name="com.step2energy.app.ui.EnergyFragment"
        android:label="Energy"
        tools:layout="@layout/fragment_energy" />

    <fragment
        android:id="@+id/settingsFragment"
        android:name="com.step2energy.app.ui.SettingsFragment"
        android:label="Settings"
        tools:layout="@layout/fragment_settings" />

</navigation>
