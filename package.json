{"name": "step2energy", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "expo": "~49.0.0", "expo-sensors": "~12.3.0", "expo-status-bar": "~1.6.0", "expo-secure-store": "~12.3.1", "react": "18.2.0", "react-native": "0.72.6", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "babel-preset-expo": "~9.5.0", "typescript": "^5.1.3"}, "private": true}