{"name": "step2energy", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "expo": "~48.0.18", "expo-sensors": "~12.1.1", "expo-status-bar": "~1.4.4", "expo-secure-store": "~12.1.1", "react": "18.2.0", "react-native": "0.71.8", "react-native-safe-area-context": "4.5.0", "react-native-screens": "~3.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.0.14", "typescript": "^4.9.4"}, "private": true}