package com.step2energy.app.viewmodel

import androidx.lifecycle.*
import com.step2energy.app.model.User
import com.step2energy.app.repository.UserRepository
import kotlinx.coroutines.launch

class UserViewModel(private val repository: UserRepository) : ViewModel() {
    
    // User data
    val user: LiveData<User?> = repository.user
    
    // Save user data
    fun saveUser(weight: Float, height: Float, gender: String, useMetricSystem: Boolean = true) {
        viewModelScope.launch {
            val newUser = User(
                weight = weight,
                height = height,
                gender = gender,
                useMetricSystem = useMetricSystem
            )
            repository.insert(newUser)
        }
    }
    
    // Update user data
    fun updateUser(user: User) {
        viewModelScope.launch {
            repository.update(user)
        }
    }
    
    // Delete all user data
    fun deleteAllUserData() {
        viewModelScope.launch {
            repository.deleteAll()
        }
    }
    
    // Check if user profile exists
    fun hasUserProfile(): LiveData<Boolean> {
        return Transformations.map(user) { it != null }
    }
    
    // Factory for creating UserViewModel with dependency injection
    class UserViewModelFactory(private val repository: UserRepository) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(UserViewModel::class.java)) {
                @Suppress("UNCHECKED_CAST")
                return UserViewModel(repository) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
}
