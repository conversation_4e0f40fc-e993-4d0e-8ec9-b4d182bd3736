package com.step2energy.app.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.step2energy.app.Step2EnergyApp
import com.step2energy.app.databinding.FragmentHomeBinding
import com.step2energy.app.viewmodel.StepDataViewModel
import java.text.NumberFormat
import java.util.*

class HomeFragment : Fragment() {
    
    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!
    
    private val stepDataViewModel: StepDataViewModel by viewModels {
        val app = requireActivity().application as Step2EnergyApp
        StepDataViewModel.StepDataViewModelFactory(app.stepDataRepository, app.userRepository)
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Observe today's step data
        stepDataViewModel.todayStepData.observe(viewLifecycleOwner) { stepData ->
            if (stepData != null) {
                // Format numbers with commas
                val numberFormat = NumberFormat.getNumberInstance(Locale.getDefault())
                
                // Update steps
                binding.stepsCount.text = numberFormat.format(stepData.steps)
                
                // Update calories
                val caloriesText = String.format("%.1f kcal", stepData.caloriesBurned)
                binding.caloriesValue.text = caloriesText
                
                // Update joules
                val joulesText = numberFormat.format(stepData.joulesProduced.toInt()) + " J"
                binding.joulesValue.text = joulesText
                
                // Update kWh
                val kwhValue = stepDataViewModel.joulesToKWh(stepData.joulesProduced)
                val kwhText = String.format("%.3f kWh", kwhValue)
                binding.kwhValue.text = kwhText
                
                // Update device equivalents
                updateDeviceEquivalents(stepData.joulesProduced)
            } else {
                // Set default values if no data
                binding.stepsCount.text = "0"
                binding.caloriesValue.text = "0 kcal"
                binding.joulesValue.text = "0 J"
                binding.kwhValue.text = "0 kWh"
                
                // Set default device equivalents
                binding.ledValue.text = "0 minutes"
                binding.fanValue.text = "0 minutes"
                binding.phoneValue.text = "0%"
            }
        }
    }
    
    private fun updateDeviceEquivalents(joules: Float) {
        val deviceEquivalents = stepDataViewModel.getDevicePowerEquivalents(joules)
        
        for (device in deviceEquivalents) {
            val deviceName = device["device"] as String
            val value = device["value"] as Float
            val unit = device["unit"] as String
            
            when (deviceName) {
                "5W LED Bulb" -> {
                    val formattedValue = String.format("%.1f %s", value, unit)
                    binding.ledValue.text = formattedValue
                }
                "Fan" -> {
                    val formattedValue = String.format("%.1f %s", value, unit)
                    binding.fanValue.text = formattedValue
                }
                "Phone Charger" -> {
                    val formattedValue = String.format("%.1f%s", value, "%")
                    binding.phoneValue.text = formattedValue
                }
            }
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
