# Step2Energy - Expo App

Step2Energy is a mobile application built with React Native and Expo that tracks your steps and converts them into energy metrics, showing you how much electrical energy your physical activity could theoretically generate.

## 🚀 Quick Start

### Prerequisites
1. Install [Node.js](https://nodejs.org/) (includes npm)
2. Install Expo CLI: `npm install -g expo-cli`
3. Install [Expo Go](https://expo.dev/client) app on your mobile device

### Running the App
1. Open terminal/command prompt
2. Navigate to project directory: `cd "C:\Users\<USER>\Desktop\Energycalories"`
3. Install dependencies: `npm install`
4. Start the app: `npx expo start`
5. Scan QR code with Expo Go app on your device

### Alternative: One-Click Start (Windows)
Double-click `start.bat` file to automatically install dependencies and start the app.

## Features

### Pedometer (Step Counter)
- Tracks steps using the phone's step sensor
- Displays steps in real-time
- Shows daily, weekly, and monthly step summaries

### Calorie Counter
- Estimates calories burned based on steps taken
- Uses the formula: Calories burned = steps × weight (kg) × 0.0005
- Collects user weight, height, and gender at setup

### Energy Conversion
- Converts burned calories into electrical energy using the formula: 1 kilocalorie = 4184 joules
- Shows energy in both joules and kilowatt-hours (kWh)

### Device Power Equivalents
- Shows how long your energy could power various devices:
  - A 5W LED bulb
  - A fan
  - Phone charging percentage
- Provides fun visual comparisons

### Dashboard
- Steps today
- Calories burned
- Energy produced (in joules/kWh)
- "Devices powered" section

### Settings
- Dark mode toggle
- Unit preferences (metric/imperial)
- Data reset option

## Technical Details

- Platform: iOS and Android
- Framework: React Native with Expo
- State Management: React Context API
- Storage: Expo SecureStore
- Sensors: Expo Pedometer

## Requirements

- Node.js and npm
- Expo CLI
- iOS or Android device with Expo Go app installed (or emulator)

## Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Start the Expo development server:
   ```
   npx expo start
   ```
4. Scan the QR code with the Expo Go app on your device or run on an emulator

## Permissions

The app requires the following permissions:
- ACTIVITY_RECOGNITION: To access the step counter sensor

## Running with Expo

This app is designed to be run with Expo, which provides a simplified development workflow for React Native apps. You can run it in several ways:

1. **Expo Go App**: The easiest way to run the app is using the Expo Go app on your physical device. Start the Expo server with `npx expo start` and scan the QR code.

2. **Expo Snack**: You can also run the app in your browser using Expo Snack, which is an online editor for React Native.

3. **Expo Development Build**: For a more native experience, you can create a development build with `eas build --profile development --platform android` (or ios).

## License

This project is licensed under the MIT License - see the LICENSE file for details.
