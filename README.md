# Step2Energy Android App

Step2Energy is an Android application that tracks your steps and converts them into energy metrics, showing you how much electrical energy your physical activity could theoretically generate.

## Features

### Pedometer (Step Counter)
- Tracks steps using the phone's step sensor
- Displays steps in real-time
- Shows daily, weekly, and monthly step summaries

### Calorie Counter
- Estimates calories burned based on steps taken
- Uses the formula: Calories burned = steps × weight (kg) × 0.0005
- Collects user weight, height, and gender at setup

### Energy Conversion
- Converts burned calories into electrical energy using the formula: 1 kilocalorie = 4184 joules
- Shows energy in both joules and kilowatt-hours (kWh)

### Device Power Equivalents
- Shows how long your energy could power various devices:
  - A 5W LED bulb
  - A fan
  - Phone charging percentage
- Provides fun visual comparisons

### Dashboard
- Steps today
- Calories burned
- Energy produced (in joules/kWh)
- "Devices powered" section

### Settings
- Dark mode toggle
- Unit preferences (metric/imperial)
- Data reset option

## Technical Details

- Platform: Android
- Language: Kotlin
- Architecture: MVVM with Repository pattern
- Database: Room for local storage
- Sensors: Step counter sensor
- UI: Material Design components

## Requirements

- Android 5.0 (API level 21) or higher
- Device with step counter sensor

## Installation

1. Clone the repository
2. Open the project in Android Studio
3. Build and run the app on your device

## Permissions

The app requires the following permissions:
- ACTIVITY_RECOGNITION: To access the step counter sensor

## License

This project is licensed under the MIT License - see the LICENSE file for details.
