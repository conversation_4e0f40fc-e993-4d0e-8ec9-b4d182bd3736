package com.step2energy.app.data

import androidx.lifecycle.LiveData
import androidx.room.*
import com.step2energy.app.model.User

@Dao
interface UserDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(user: User)
    
    @Update
    suspend fun update(user: User)
    
    @Delete
    suspend fun delete(user: User)
    
    @Query("SELECT * FROM user_table LIMIT 1")
    fun getUser(): LiveData<User?>
    
    @Query("DELETE FROM user_table")
    suspend fun deleteAll()
}
