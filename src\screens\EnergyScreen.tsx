import React from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  ScrollView, 
  ActivityIndicator,
  Image
} from 'react-native';
import { useStepData } from '../context/StepDataContext';
import { formatNumberWithCommas, joulesToKWh } from '../utils/energyCalculations';
import { Ionicons } from '@expo/vector-icons';

const EnergyScreen = () => {
  const { 
    weeklyStepData, 
    getDevicePowerEquivalents,
    isLoading 
  } = useStepData();
  
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
      </View>
    );
  }
  
  // Calculate total joules for the week
  const totalJoules = weeklyStepData.reduce(
    (sum, data) => sum + data.joulesProduced, 
    0
  );
  
  // Get device power equivalents
  const deviceEquivalents = getDevicePowerEquivalents(totalJoules);
  
  // Find specific device data
  const ledData = deviceEquivalents.find(d => d.deviceName === '5W LED Bulb');
  const fanData = deviceEquivalents.find(d => d.deviceName === 'Fan');
  const phoneData = deviceEquivalents.find(d => d.deviceName === 'Phone Charge');
  
  // Format LED time
  const formatLedTime = () => {
    if (!ledData) return '0 minutes';
    
    const minutes = ledData.value;
    if (minutes < 60) {
      return `${minutes.toFixed(1)} minutes`;
    } else {
      const hours = minutes / 60;
      return `${hours.toFixed(1)} hours`;
    }
  };
  
  // Format fan time
  const formatFanTime = () => {
    if (!fanData) return '0 minutes';
    
    const minutes = fanData.value;
    if (minutes < 60) {
      return `${minutes.toFixed(1)} minutes`;
    } else {
      const hours = minutes / 60;
      return `${hours.toFixed(1)} hours`;
    }
  };
  
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Energy Equivalents</Text>
      
      {/* Summary Card */}
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Energy Produced (Last 7 Days)</Text>
        <Text style={styles.joulesValue}>
          {formatNumberWithCommas(Math.round(totalJoules))} J
        </Text>
        <Text style={styles.kwhValue}>
          {joulesToKWh(totalJoules).toFixed(5)} kWh
        </Text>
      </View>
      
      {/* LED Bulb Card */}
      <View style={styles.deviceCard}>
        <View style={styles.deviceIconContainer}>
          <Ionicons name="bulb" size={36} color="#FFC107" />
        </View>
        <View style={styles.deviceContent}>
          <Text style={styles.deviceTitle}>5W LED Bulb</Text>
          <Text style={styles.deviceDescription}>
            Your energy could power a 5W LED bulb for {formatLedTime()}!
          </Text>
          <View style={styles.deviceInfoContainer}>
            <Text style={styles.deviceInfo}>
              That's enough to light up a room for {formatLedTime()}.
            </Text>
          </View>
        </View>
      </View>
      
      {/* Fan Card */}
      <View style={styles.deviceCard}>
        <View style={styles.deviceIconContainer}>
          <Ionicons name="fan" size={36} color="#2196F3" />
        </View>
        <View style={styles.deviceContent}>
          <Text style={styles.deviceTitle}>Fan</Text>
          <Text style={styles.deviceDescription}>
            Your energy could power a fan for {formatFanTime()}!
          </Text>
          <View style={styles.deviceInfoContainer}>
            <Text style={styles.deviceInfo}>
              That's enough to keep you cool on a hot day.
            </Text>
          </View>
        </View>
      </View>
      
      {/* Phone Card */}
      <View style={styles.deviceCard}>
        <View style={styles.deviceIconContainer}>
          <Ionicons name="phone-portrait" size={36} color="#9C27B0" />
        </View>
        <View style={styles.deviceContent}>
          <Text style={styles.deviceTitle}>Phone Charge</Text>
          <Text style={styles.deviceDescription}>
            Your energy could charge your phone from 0% to {phoneData ? phoneData.value.toFixed(1) : '0'}%!
          </Text>
          <View style={styles.deviceInfoContainer}>
            <Text style={styles.deviceInfo}>
              That's {phoneData ? (phoneData.value / 100).toFixed(2) : '0'} full charges of a typical smartphone.
            </Text>
          </View>
        </View>
      </View>
      
      {/* Fun Fact Card */}
      <View style={styles.funFactCard}>
        <Text style={styles.funFactTitle}>Did You Know?</Text>
        <Text style={styles.funFactText}>
          The average person takes about 4,000 to 6,000 steps per day. If everyone converted their steps to energy, we could power millions of LED bulbs!
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 16,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  cardTitle: {
    fontSize: 16,
    color: '#757575',
    marginBottom: 8,
  },
  joulesValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#388E3C',
  },
  kwhValue: {
    fontSize: 16,
    color: '#757575',
    marginTop: 4,
  },
  deviceCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    flexDirection: 'row',
  },
  deviceIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  deviceContent: {
    flex: 1,
  },
  deviceTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 4,
  },
  deviceDescription: {
    fontSize: 14,
    color: '#212121',
    marginBottom: 8,
  },
  deviceInfoContainer: {
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    padding: 8,
  },
  deviceInfo: {
    fontSize: 12,
    color: '#757575',
  },
  funFactCard: {
    backgroundColor: '#E8F5E9',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  funFactTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#388E3C',
    marginBottom: 8,
  },
  funFactText: {
    fontSize: 14,
    color: '#212121',
    lineHeight: 20,
  },
});

export default EnergyScreen;
