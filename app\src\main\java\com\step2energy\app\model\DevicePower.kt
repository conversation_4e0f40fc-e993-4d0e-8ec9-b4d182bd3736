package com.step2energy.app.model

data class DevicePower(
    val deviceName: String,
    val powerWatts: Float,
    val iconResId: Int
) {
    // Calculate how long a device could be powered with the given energy in joules
    fun calculateDurationMinutes(joules: Float): Float {
        // Energy (joules) = Power (watts) * Time (seconds)
        // Time (seconds) = Energy (joules) / Power (watts)
        // Convert seconds to minutes
        return joules / (powerWatts * 60)
    }
    
    // Calculate phone charge percentage (assuming a typical phone battery is 10Wh or 36000 joules)
    fun calculatePhoneChargePercentage(joules: Float): Float {
        val phoneFullChargeJoules = 36000f // 10Wh = 36000 joules
        return (joules / phoneFullChargeJoules) * 100
    }
    
    companion object {
        // Define common devices and their power consumption
        fun getCommonDevices(): List<DevicePower> {
            return listOf(
                DevicePower("5W LED Bulb", 5f, 0), // Replace 0 with actual resource ID
                DevicePower("Fan", 30f, 0),        // Replace 0 with actual resource ID
                DevicePower("Phone Charger", 5f, 0) // Replace 0 with actual resource ID
            )
        }
    }
}
