<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.step2energy.app">

    <!-- Permissions for step counter sensor -->
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
    <uses-feature android:name="android.hardware.sensor.stepcounter" android:required="true" />
    
    <application
        android:name=".Step2EnergyApp"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Step2Energy">
        
        <activity
            android:name=".ui.MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <service
            android:name=".service.StepCounterService"
            android:enabled="true"
            android:exported="false" />
    </application>

</manifest>
