<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.HomeFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Steps Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/steps_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <TextView
                    android:id="@+id/steps_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/steps_today"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/steps_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline3"
                    android:textColor="@color/primary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/steps_label"
                    tools:text="5,432" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Calories Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/calories_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/steps_card">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <TextView
                    android:id="@+id/calories_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/calories_burned"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/calories_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline4"
                    android:textColor="@color/accent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/calories_label"
                    tools:text="245 kcal" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Energy Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/energy_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/calories_card">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <TextView
                    android:id="@+id/energy_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/energy_produced"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/joules_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                    android:textColor="@color/primary_dark"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/energy_label"
                    tools:text="1,025,080 J" />

                <TextView
                    android:id="@+id/kwh_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/joules_value"
                    tools:text="0.28 kWh" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Devices Powered Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/devices_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/energy_card">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <TextView
                    android:id="@+id/devices_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/devices_powered"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/led_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/led_bulb"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/devices_label" />

                <TextView
                    android:id="@+id/led_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                    android:textStyle="bold"
                    app:layout_constraintBaseline_toBaselineOf="@+id/led_label"
                    app:layout_constraintStart_toEndOf="@+id/led_label"
                    tools:text="10 minutes" />

                <TextView
                    android:id="@+id/fan_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/fan"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/led_label" />

                <TextView
                    android:id="@+id/fan_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                    android:textStyle="bold"
                    app:layout_constraintBaseline_toBaselineOf="@+id/fan_label"
                    app:layout_constraintStart_toEndOf="@+id/fan_label"
                    tools:text="3 minutes" />

                <TextView
                    android:id="@+id/phone_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/phone_charge"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/fan_label" />

                <TextView
                    android:id="@+id/phone_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                    android:textStyle="bold"
                    app:layout_constraintBaseline_toBaselineOf="@+id/phone_label"
                    app:layout_constraintStart_toEndOf="@+id/phone_label"
                    tools:text="2.5%" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
