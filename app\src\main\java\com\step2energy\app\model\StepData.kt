package com.step2energy.app.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.time.LocalDate

@Entity(tableName = "step_data_table")
data class StepData(
    @PrimaryKey(autoGenerate = true)
    val id: Int = 0,
    val date: String, // ISO format: YYYY-MM-DD
    val steps: Int,
    val caloriesBurned: Float,
    val joulesProduced: Float
) {
    companion object {
        // Calculate calories burned based on steps and weight
        fun calculateCalories(steps: Int, weightKg: Float): Float {
            return steps * weightKg * 0.0005f
        }
        
        // Convert calories to joules
        fun convertCaloriesToJoules(calories: Float): Float {
            return calories * 4184f
        }
    }
}
