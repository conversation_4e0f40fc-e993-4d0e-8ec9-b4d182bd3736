@echo off
echo Starting Step2Energy Expo App...
echo.

echo Checking if npm is installed...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not installed!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Checking if Expo CLI is installed...
expo --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing Expo CLI...
    npm install -g expo-cli
)

echo Installing dependencies...
npm install

echo Starting Expo development server...
npx expo start

pause
