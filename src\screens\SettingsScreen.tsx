import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  Switch, 
  TouchableOpacity, 
  Alert,
  ScrollView,
  ActivityIndicator
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../App';
import { useUser } from '../context/UserContext';
import { useStepData } from '../context/StepDataContext';
import { Ionicons } from '@expo/vector-icons';

type SettingsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const SettingsScreen = () => {
  const navigation = useNavigation<SettingsScreenNavigationProp>();
  const { user, updateUser, deleteUser, isLoading: userLoading } = useUser();
  const { resetAllStepData, isLoading: stepDataLoading } = useStepData();
  
  const [darkMode, setDarkMode] = useState(false);
  const [useMetric, setUseMetric] = useState(true);
  
  // Set initial values from user data
  useEffect(() => {
    if (user) {
      setUseMetric(user.useMetricSystem);
    }
  }, [user]);
  
  // Handle dark mode toggle
  const handleDarkModeToggle = (value: boolean) => {
    setDarkMode(value);
    // In a real app, you would apply the theme change here
    // For this example, we'll just update the state
  };
  
  // Handle unit system toggle
  const handleUnitSystemToggle = async (value: boolean) => {
    if (!user) return;
    
    setUseMetric(value);
    
    try {
      await updateUser({
        ...user,
        useMetricSystem: value
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to update unit preferences');
      console.error(error);
    }
  };
  
  // Handle reset data
  const handleResetData = () => {
    Alert.alert(
      'Reset Data',
      'Are you sure you want to reset all your data? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              await resetAllStepData();
              await deleteUser();
              
              // Navigate back to onboarding
              navigation.replace('Onboarding');
            } catch (error) {
              Alert.alert('Error', 'Failed to reset data');
              console.error(error);
            }
          }
        }
      ]
    );
  };
  
  if (userLoading || stepDataLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
      </View>
    );
  }
  
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Settings</Text>
      
      {/* Dark Mode */}
      <View style={styles.card}>
        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Ionicons name="moon-outline" size={24} color="#212121" style={styles.settingIcon} />
            <Text style={styles.settingLabel}>Dark Mode</Text>
          </View>
          <Switch
            value={darkMode}
            onValueChange={handleDarkModeToggle}
            trackColor={{ false: '#BDBDBD', true: '#8BC34A' }}
            thumbColor={darkMode ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
      </View>
      
      {/* Unit Preferences */}
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Unit Preferences</Text>
        
        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Ionicons name="options-outline" size={24} color="#212121" style={styles.settingIcon} />
            <Text style={styles.settingLabel}>Use Metric System</Text>
          </View>
          <Switch
            value={useMetric}
            onValueChange={handleUnitSystemToggle}
            trackColor={{ false: '#BDBDBD', true: '#8BC34A' }}
            thumbColor={useMetric ? '#4CAF50' : '#f4f3f4'}
          />
        </View>
        
        <Text style={styles.unitDescription}>
          {useMetric ? 'Using kilograms (kg) and centimeters (cm)' : 'Using pounds (lb) and inches (in)'}
        </Text>
      </View>
      
      {/* User Info */}
      {user && (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Your Profile</Text>
          
          <View style={styles.profileRow}>
            <Text style={styles.profileLabel}>Weight:</Text>
            <Text style={styles.profileValue}>
              {user.weight} {useMetric ? 'kg' : 'lb'}
            </Text>
          </View>
          
          <View style={styles.profileRow}>
            <Text style={styles.profileLabel}>Height:</Text>
            <Text style={styles.profileValue}>
              {user.height} {useMetric ? 'cm' : 'in'}
            </Text>
          </View>
          
          <View style={styles.profileRow}>
            <Text style={styles.profileLabel}>Gender:</Text>
            <Text style={styles.profileValue}>
              {user.gender.charAt(0).toUpperCase() + user.gender.slice(1)}
            </Text>
          </View>
        </View>
      )}
      
      {/* Reset Data */}
      <TouchableOpacity style={styles.resetButton} onPress={handleResetData}>
        <Text style={styles.resetButtonText}>Reset All Data</Text>
      </TouchableOpacity>
      
      {/* App Info */}
      <View style={styles.appInfoContainer}>
        <Text style={styles.appName}>Step2Energy</Text>
        <Text style={styles.appVersion}>Version 1.0.0</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 16,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  cardTitle: {
    fontSize: 16,
    color: '#757575',
    marginBottom: 16,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingIcon: {
    marginRight: 12,
  },
  settingLabel: {
    fontSize: 16,
    color: '#212121',
  },
  unitDescription: {
    fontSize: 12,
    color: '#757575',
    marginTop: 4,
    marginLeft: 36,
  },
  profileRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  profileLabel: {
    fontSize: 16,
    color: '#757575',
    width: 80,
  },
  profileValue: {
    fontSize: 16,
    color: '#212121',
    fontWeight: '500',
  },
  resetButton: {
    backgroundColor: '#F44336',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  resetButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  appInfoContainer: {
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 32,
  },
  appName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  appVersion: {
    fontSize: 14,
    color: '#757575',
    marginTop: 4,
  },
});

export default SettingsScreen;
