package com.step2energy.app.data

import androidx.lifecycle.LiveData
import androidx.room.*
import com.step2energy.app.model.StepData

@Dao
interface StepDataDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(stepData: StepData)
    
    @Update
    suspend fun update(stepData: StepData)
    
    @Query("SELECT * FROM step_data_table WHERE date = :date LIMIT 1")
    fun getStepDataForDate(date: String): LiveData<StepData?>
    
    @Query("SELECT * FROM step_data_table ORDER BY date DESC")
    fun getAllStepData(): LiveData<List<StepData>>
    
    @Query("SELECT * FROM step_data_table WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    fun getStepDataBetweenDates(startDate: String, endDate: String): LiveData<List<StepData>>
    
    @Query("SELECT SUM(steps) FROM step_data_table WHERE date BETWEEN :startDate AND :endDate")
    fun getTotalStepsBetweenDates(startDate: String, endDate: String): LiveData<Int?>
    
    @Query("SELECT SUM(caloriesBurned) FROM step_data_table WHERE date BETWEEN :startDate AND :endDate")
    fun getTotalCaloriesBetweenDates(startDate: String, endDate: String): LiveData<Float?>
    
    @Query("SELECT SUM(joulesProduced) FROM step_data_table WHERE date BETWEEN :startDate AND :endDate")
    fun getTotalJoulesBetweenDates(startDate: String, endDate: String): LiveData<Float?>
    
    @Query("DELETE FROM step_data_table")
    suspend fun deleteAll()
}
