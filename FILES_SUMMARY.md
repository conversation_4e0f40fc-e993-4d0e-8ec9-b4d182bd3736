# Step2Energy - Files Summary

## Project Structure

```
Step2Energy/
├── assets/
│   ├── icon.png (placeholder)
│   ├── splash.png (placeholder)
│   ├── adaptive-icon.png (placeholder)
│   └── favicon.png (placeholder)
├── src/
│   ├── context/
│   │   ├── UserContext.tsx
│   │   └── StepDataContext.tsx
│   ├── screens/
│   │   ├── OnboardingScreen.tsx
│   │   ├── HomeScreen.tsx
│   │   ├── EnergyScreen.tsx
│   │   └── SettingsScreen.tsx
│   └── utils/
│       └── energyCalculations.ts
├── .expo/ (created automatically)
├── .gitignore
├── App.tsx (main app with navigation)
├── App-simple.tsx (simple test version)
├── app.json (Expo configuration)
├── babel.config.js
├── metro.config.js
├── package.json (dependencies)
├── tsconfig.json (TypeScript configuration)
├── README.md
├── SETUP.md
└── FILES_SUMMARY.md (this file)
```

## Key Files Description

### Configuration Files
- **package.json**: Contains all dependencies and scripts
- **app.json**: Expo app configuration
- **babel.config.js**: Babel configuration for React Native
- **tsconfig.json**: TypeScript configuration
- **metro.config.js**: Metro bundler configuration

### Main App Files
- **App.tsx**: Main application with navigation setup
- **App-simple.tsx**: Simple version for testing

### Context Files (State Management)
- **UserContext.tsx**: Manages user profile data
- **StepDataContext.tsx**: Manages step counting and energy calculations

### Screen Components
- **OnboardingScreen.tsx**: User profile setup
- **HomeScreen.tsx**: Main dashboard with steps, calories, energy
- **EnergyScreen.tsx**: Energy equivalents and fun facts
- **SettingsScreen.tsx**: App settings and data management

### Utility Files
- **energyCalculations.ts**: Energy conversion formulas

## Dependencies

### Main Dependencies
- expo: ~49.0.0
- react: 18.2.0
- react-native: 0.72.6
- @react-navigation/native: ^6.1.7
- @react-navigation/bottom-tabs: ^6.5.8
- @react-navigation/native-stack: ^6.9.13
- expo-sensors: ~12.3.0
- expo-secure-store: ~12.3.1
- @expo/vector-icons: ^13.0.0

### Dev Dependencies
- @babel/core: ^7.20.0
- babel-preset-expo: ~9.5.0
- typescript: ^5.1.3
- @types/react: ~18.2.14

## Features Implemented

1. **Step Counting**: Uses Expo Pedometer API
2. **Energy Calculations**: Converts steps to calories to joules
3. **Device Equivalents**: Shows how long energy could power devices
4. **User Profile**: Weight, height, gender storage
5. **Data Persistence**: Uses Expo SecureStore
6. **Navigation**: Bottom tab navigation
7. **Settings**: Dark mode toggle, unit preferences, data reset

## To Run the App

1. Install Node.js and npm
2. Install Expo CLI: `npm install -g expo-cli`
3. Navigate to project directory
4. Run: `npm install`
5. Run: `npx expo start`
6. Scan QR code with Expo Go app on your device

## Alternative: Expo Snack

You can also run this app in Expo Snack (online):
1. Go to snack.expo.dev
2. Copy all the files into a new project
3. Run directly in browser or on device
