# Step2Energy - Setup Instructions

## Prerequisites

Before you can run the Step2Energy app with Expo, you need to install the following:

### 1. Install Node.js and npm
- Download Node.js from [nodejs.org](https://nodejs.org/)
- Choose the LTS (Long Term Support) version
- Run the installer and follow the installation steps
- This will install both Node.js and npm

### 2. Verify Installation
Open a command prompt or PowerShell and run:
```bash
node --version
npm --version
```

### 3. Install Expo CLI
```bash
npm install -g expo-cli
```

### 4. Install Expo Go on your mobile device
- **iOS**: Download from the [App Store](https://apps.apple.com/app/apple-store/id982107779)
- **Android**: Download from [Google Play Store](https://play.google.com/store/apps/details?id=host.exp.exponent)

## Running the App

### Option 1: Full App (Recommended)
1. Navigate to the project directory:
   ```bash
   cd "C:\Users\<USER>\Desktop\Energycalories"
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the Expo development server:
   ```bash
   npx expo start
   ```

4. Scan the QR code with your mobile device:
   - **iOS**: Use the Camera app to scan the QR code
   - **Android**: Use the Expo Go app to scan the QR code

### Option 2: Simple Test App
If you want to test that Expo is working first, you can run a simple version:

1. Rename the current App.tsx to App-full.tsx:
   ```bash
   mv App.tsx App-full.tsx
   ```

2. Rename App-simple.tsx to App.tsx:
   ```bash
   mv App-simple.tsx App.tsx
   ```

3. Start the Expo development server:
   ```bash
   npx expo start
   ```

### Option 3: Using Expo Snack (No Installation Required)
If you don't want to install Node.js and npm:

1. Go to [snack.expo.dev](https://snack.expo.dev/)
2. Create a new project
3. Copy and paste the code from each file into the corresponding files in Expo Snack
4. Run the app directly in the browser or scan the QR code with Expo Go

## Troubleshooting

### Common Issues:

1. **"expo command not found"**
   - Make sure you installed Expo CLI globally: `npm install -g expo-cli`
   - Restart your command prompt/PowerShell

2. **"npm command not found"**
   - Install Node.js from nodejs.org
   - Restart your command prompt/PowerShell

3. **Dependencies not installing**
   - Try clearing npm cache: `npm cache clean --force`
   - Delete node_modules folder and run `npm install` again

4. **App not loading on device**
   - Make sure your computer and mobile device are on the same WiFi network
   - Try using the tunnel option: `npx expo start --tunnel`

5. **Pedometer not working**
   - The pedometer requires a physical device (won't work in simulator)
   - Make sure you grant motion permissions when prompted

## Development Commands

- `npx expo start` - Start the development server
- `npx expo start --android` - Start and open on Android
- `npx expo start --ios` - Start and open on iOS
- `npx expo start --web` - Start and open in web browser
- `npx expo start --tunnel` - Use tunnel for devices not on same network

## Building for Production

To build the app for production:

1. Install EAS CLI:
   ```bash
   npm install -g eas-cli
   ```

2. Login to Expo:
   ```bash
   eas login
   ```

3. Configure the build:
   ```bash
   eas build:configure
   ```

4. Build for Android:
   ```bash
   eas build --platform android
   ```

5. Build for iOS:
   ```bash
   eas build --platform ios
   ```

## Features

The Step2Energy app includes:
- Step counting using device sensors
- Calorie calculation based on user weight
- Energy conversion (calories to joules/kWh)
- Device power equivalents
- User profile management
- Dark mode support
- Data persistence

Enjoy tracking your steps and converting them to energy! 🚶‍♂️⚡
