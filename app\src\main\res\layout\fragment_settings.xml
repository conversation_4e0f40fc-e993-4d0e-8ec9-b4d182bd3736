<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".ui.SettingsFragment">

    <TextView
        android:id="@+id/settings_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/settings_title"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
        android:textColor="@color/primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Dark Mode Switch -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/dark_mode_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/settings_title">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <TextView
                android:id="@+id/dark_mode_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/dark_mode"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/dark_mode_switch"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.switchmaterial.SwitchMaterial
                android:id="@+id/dark_mode_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Unit Preferences -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/units_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dark_mode_card">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <TextView
                android:id="@+id/units_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/unit_preferences"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <RadioGroup
                android:id="@+id/units_radio_group"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/units_label">

                <RadioButton
                    android:id="@+id/radio_metric"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/metric" />

                <RadioButton
                    android:id="@+id/radio_imperial"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/imperial" />
            </RadioGroup>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Reset Data Button -->
    <Button
        android:id="@+id/reset_data_button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="@string/reset_data"
        android:backgroundTint="@android:color/holo_red_light"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/units_card" />

</androidx.constraintlayout.widget.ConstraintLayout>
