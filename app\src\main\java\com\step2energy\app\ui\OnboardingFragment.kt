package com.step2energy.app.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.step2energy.app.R
import com.step2energy.app.Step2EnergyApp
import com.step2energy.app.databinding.FragmentOnboardingBinding
import com.step2energy.app.viewmodel.UserViewModel

class OnboardingFragment : Fragment() {
    
    private var _binding: FragmentOnboardingBinding? = null
    private val binding get() = _binding!!
    
    private val userViewModel: UserViewModel by viewModels { 
        UserViewModel.UserViewModelFactory((requireActivity().application as Step2EnergyApp).userRepository) 
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentOnboardingBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Check if user profile already exists
        userViewModel.hasUserProfile().observe(viewLifecycleOwner) { hasProfile ->
            if (hasProfile) {
                // Navigate to home screen if profile exists
                findNavController().navigate(R.id.action_onboardingFragment_to_homeFragment)
            }
        }
        
        // Set up save button
        binding.saveButton.setOnClickListener {
            saveUserProfile()
        }
    }
    
    private fun saveUserProfile() {
        val weightStr = binding.weightInput.text.toString()
        val heightStr = binding.heightInput.text.toString()
        
        // Validate inputs
        if (weightStr.isEmpty() || heightStr.isEmpty()) {
            Toast.makeText(requireContext(), "Please fill in all fields", Toast.LENGTH_SHORT).show()
            return
        }
        
        // Get gender selection
        val gender = when (binding.genderRadioGroup.checkedRadioButtonId) {
            R.id.radio_male -> "male"
            R.id.radio_female -> "female"
            R.id.radio_other -> "other"
            else -> {
                Toast.makeText(requireContext(), "Please select a gender", Toast.LENGTH_SHORT).show()
                return
            }
        }
        
        try {
            val weight = weightStr.toFloat()
            val height = heightStr.toFloat()
            
            // Save user profile
            userViewModel.saveUser(weight, height, gender)
            
            // Navigate to home screen
            findNavController().navigate(R.id.action_onboardingFragment_to_homeFragment)
        } catch (e: NumberFormatException) {
            Toast.makeText(requireContext(), "Please enter valid numbers", Toast.LENGTH_SHORT).show()
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
