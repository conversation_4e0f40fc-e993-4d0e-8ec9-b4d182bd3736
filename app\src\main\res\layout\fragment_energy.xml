<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.EnergyFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/energy_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/energy_equivalents_title"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
            android:textColor="@color/primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Summary Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/summary_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/energy_title">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <TextView
                    android:id="@+id/total_energy_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/energy_produced"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/total_joules_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                    android:textColor="@color/primary_dark"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/total_energy_label"
                    tools:text="5,125,400 J" />

                <TextView
                    android:id="@+id/total_kwh_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/total_joules_value"
                    tools:text="1.42 kWh" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- LED Bulb Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/led_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/summary_card">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/led_icon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@android:drawable/ic_menu_gallery"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:contentDescription="@string/led_bulb" />

                <TextView
                    android:id="@+id/led_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="@string/led_bulb"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/led_icon"
                    app:layout_constraintTop_toTopOf="@+id/led_icon" />

                <TextView
                    android:id="@+id/led_description"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/led_title"
                    app:layout_constraintTop_toBottomOf="@+id/led_title"
                    tools:text="Your energy could power a 5W LED bulb for 17 hours!" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Fan Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/fan_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/led_card">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/fan_icon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@android:drawable/ic_menu_gallery"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:contentDescription="@string/fan" />

                <TextView
                    android:id="@+id/fan_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="@string/fan"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/fan_icon"
                    app:layout_constraintTop_toTopOf="@+id/fan_icon" />

                <TextView
                    android:id="@+id/fan_description"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/fan_title"
                    app:layout_constraintTop_toBottomOf="@+id/fan_title"
                    tools:text="Your energy could power a fan for 2.8 hours!" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Phone Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/phone_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/fan_card">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/phone_icon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@android:drawable/ic_menu_gallery"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:contentDescription="@string/phone_charge" />

                <TextView
                    android:id="@+id/phone_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="@string/phone_charge"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/phone_icon"
                    app:layout_constraintTop_toTopOf="@+id/phone_icon" />

                <TextView
                    android:id="@+id/phone_description"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/phone_title"
                    app:layout_constraintTop_toBottomOf="@+id/phone_title"
                    tools:text="Your energy could charge your phone from 0% to 14%!" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
