package com.step2energy.app.ui

import android.app.AlertDialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatDelegate
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.step2energy.app.R
import com.step2energy.app.Step2EnergyApp
import com.step2energy.app.databinding.FragmentSettingsBinding
import com.step2energy.app.model.User
import com.step2energy.app.viewmodel.StepDataViewModel
import com.step2energy.app.viewmodel.UserViewModel

class SettingsFragment : Fragment() {
    
    private var _binding: FragmentSettingsBinding? = null
    private val binding get() = _binding!!
    
    private val userViewModel: UserViewModel by viewModels { 
        UserViewModel.UserViewModelFactory((requireActivity().application as Step2EnergyApp).userRepository) 
    }
    
    private val stepDataViewModel: StepDataViewModel by viewModels {
        val app = requireActivity().application as Step2EnergyApp
        StepDataViewModel.StepDataViewModelFactory(app.stepDataRepository, app.userRepository)
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSettingsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Set up dark mode switch
        setupDarkModeSwitch()
        
        // Set up unit preferences
        setupUnitPreferences()
        
        // Set up reset data button
        binding.resetDataButton.setOnClickListener {
            showResetConfirmationDialog()
        }
    }
    
    private fun setupDarkModeSwitch() {
        // Get current night mode state
        val sharedPrefs = requireActivity().getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        val isDarkMode = sharedPrefs.getBoolean("dark_mode", false)
        
        // Set switch state
        binding.darkModeSwitch.isChecked = isDarkMode
        
        // Set switch listener
        binding.darkModeSwitch.setOnCheckedChangeListener { _, isChecked ->
            // Save preference
            sharedPrefs.edit().putBoolean("dark_mode", isChecked).apply()
            
            // Apply night mode
            if (isChecked) {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
            } else {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
            }
        }
    }
    
    private fun setupUnitPreferences() {
        // Observe user data
        userViewModel.user.observe(viewLifecycleOwner) { user ->
            user?.let {
                // Set radio button based on user preference
                if (it.useMetricSystem) {
                    binding.radioMetric.isChecked = true
                } else {
                    binding.radioImperial.isChecked = true
                }
            }
        }
        
        // Set radio group listener
        binding.unitsRadioGroup.setOnCheckedChangeListener { _, checkedId ->
            userViewModel.user.value?.let { user ->
                val useMetric = checkedId == R.id.radio_metric
                if (user.useMetricSystem != useMetric) {
                    // Update user preference
                    val updatedUser = User(
                        id = user.id,
                        weight = user.weight,
                        height = user.height,
                        gender = user.gender,
                        useMetricSystem = useMetric
                    )
                    userViewModel.updateUser(updatedUser)
                }
            }
        }
    }
    
    private fun showResetConfirmationDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle("Reset Data")
            .setMessage("Are you sure you want to reset all your data? This action cannot be undone.")
            .setPositiveButton("Reset") { _, _ ->
                resetAllData()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    private fun resetAllData() {
        // Reset step data
        stepDataViewModel.resetAllStepData()
        
        // Reset user data
        userViewModel.deleteAllUserData()
        
        // Navigate back to onboarding
        findNavController().navigate(R.id.onboardingFragment)
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
