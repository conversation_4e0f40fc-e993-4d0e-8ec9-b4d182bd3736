import React, { createContext, useState, useEffect, useContext } from 'react';
import * as SecureStore from 'expo-secure-store';

// User type definition
export interface User {
  weight: number;
  height: number;
  gender: 'male' | 'female' | 'other';
  useMetricSystem: boolean;
}

// Context type definition
interface UserContextType {
  user: User | null;
  saveUser: (userData: User) => Promise<void>;
  updateUser: (userData: User) => Promise<void>;
  deleteUser: () => Promise<void>;
  isLoading: boolean;
}

// Create context
const UserContext = createContext<UserContextType | undefined>(undefined);

// User provider component
export const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load user data on mount
  useEffect(() => {
    const loadUser = async () => {
      try {
        const userData = await SecureStore.getItemAsync('user_data');
        if (userData) {
          setUser(JSON.parse(userData));
        }
      } catch (error) {
        console.error('Failed to load user data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  // Save user data
  const saveUser = async (userData: User) => {
    try {
      await SecureStore.setItemAsync('user_data', JSON.stringify(userData));
      setUser(userData);
    } catch (error) {
      console.error('Failed to save user data:', error);
      throw error;
    }
  };

  // Update user data
  const updateUser = async (userData: User) => {
    try {
      await SecureStore.setItemAsync('user_data', JSON.stringify(userData));
      setUser(userData);
    } catch (error) {
      console.error('Failed to update user data:', error);
      throw error;
    }
  };

  // Delete user data
  const deleteUser = async () => {
    try {
      await SecureStore.deleteItemAsync('user_data');
      setUser(null);
    } catch (error) {
      console.error('Failed to delete user data:', error);
      throw error;
    }
  };

  return (
    <UserContext.Provider value={{ user, saveUser, updateUser, deleteUser, isLoading }}>
      {children}
    </UserContext.Provider>
  );
};

// Custom hook to use the user context
export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
