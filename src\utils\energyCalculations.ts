/**
 * Calculate calories burned based on steps and weight
 * @param steps Number of steps
 * @param weightKg Weight in kilograms
 * @returns Calories burned
 */
export const calculateCalories = (steps: number, weightKg: number): number => {
  return steps * weightKg * 0.0005;
};

/**
 * Convert calories to joules
 * @param calories Calories
 * @returns Joules
 */
export const convertCaloriesToJoules = (calories: number): number => {
  return calories * 4184;
};

/**
 * Convert joules to kilowatt-hours
 * @param joules Joules
 * @returns Kilowatt-hours
 */
export const joulesToKWh = (joules: number): number => {
  return joules / 3600000; // 1 kWh = 3,600,000 joules
};

/**
 * Calculate how long a device could be powered with the given energy in joules
 * @param joules Energy in joules
 * @param powerWatts Device power in watts
 * @returns Duration in minutes
 */
export const calculateDurationMinutes = (joules: number, powerWatts: number): number => {
  // Energy (joules) = Power (watts) * Time (seconds)
  // Time (seconds) = Energy (joules) / Power (watts)
  // Convert seconds to minutes
  return joules / (powerWatts * 60);
};

/**
 * Calculate phone charge percentage
 * @param joules Energy in joules
 * @returns Charge percentage
 */
export const calculatePhoneChargePercentage = (joules: number): number => {
  const phoneFullChargeJoules = 36000; // 10Wh = 36000 joules
  return (joules / phoneFullChargeJoules) * 100;
};

/**
 * Format number with commas
 * @param num Number to format
 * @returns Formatted number string
 */
export const formatNumberWithCommas = (num: number): string => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};
