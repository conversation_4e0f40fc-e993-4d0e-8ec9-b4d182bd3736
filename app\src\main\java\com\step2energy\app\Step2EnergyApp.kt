package com.step2energy.app

import android.app.Application
import com.step2energy.app.data.AppDatabase
import com.step2energy.app.repository.StepDataRepository
import com.step2energy.app.repository.UserRepository

class Step2EnergyApp : Application() {
    
    // Lazy initialization of database
    private val database by lazy { AppDatabase.getDatabase(this) }
    
    // Repositories
    val userRepository by lazy { UserRepository(database.userDao()) }
    val stepDataRepository by lazy { StepDataRepository(database.stepDataDao()) }
    
    override fun onCreate() {
        super.onCreate()
        // Any application-wide initialization can go here
    }
}
