package com.step2energy.app.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.step2energy.app.R
import com.step2energy.app.repository.StepDataRepository
import com.step2energy.app.repository.UserRepository
import com.step2energy.app.ui.MainActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.format.DateTimeFormatter

class StepCounterService : Service(), SensorEventListener {
    
    private lateinit var sensorManager: SensorManager
    private var stepSensor: Sensor? = null
    private var initialSteps: Int = -1
    private var currentSteps: Int = 0
    
    private lateinit var stepDataRepository: StepDataRepository
    private lateinit var userRepository: UserRepository
    
    private val serviceScope = CoroutineScope(Dispatchers.IO)
    
    companion object {
        private const val NOTIFICATION_ID = 1
        private const val CHANNEL_ID = "step_counter_channel"
    }
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize sensor manager
        sensorManager = getSystemService(Context.SENSOR_SERVICE) as SensorManager
        stepSensor = sensorManager.getDefaultSensor(Sensor.TYPE_STEP_COUNTER)
        
        // Initialize repositories
        val appDatabase = com.step2energy.app.data.AppDatabase.getDatabase(this)
        stepDataRepository = StepDataRepository(appDatabase.stepDataDao())
        userRepository = UserRepository(appDatabase.userDao())
        
        // Create notification channel for Android O and above
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // Register step counter sensor
        stepSensor?.let {
            sensorManager.registerListener(this, it, SensorManager.SENSOR_DELAY_NORMAL)
        }
        
        // Start as a foreground service with notification
        startForeground(NOTIFICATION_ID, createNotification())
        
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
    
    override fun onDestroy() {
        super.onDestroy()
        sensorManager.unregisterListener(this)
    }
    
    override fun onSensorChanged(event: SensorEvent?) {
        event?.let {
            if (it.sensor.type == Sensor.TYPE_STEP_COUNTER) {
                val totalSteps = it.values[0].toInt()
                
                // Initialize initial steps if not set
                if (initialSteps == -1) {
                    initialSteps = totalSteps
                }
                
                // Calculate current steps
                currentSteps = totalSteps - initialSteps
                
                // Update step data in database
                updateStepData(currentSteps)
                
                // Update notification
                updateNotification(currentSteps)
            }
        }
    }
    
    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // Not needed for step counter
    }
    
    private fun updateStepData(steps: Int) {
        serviceScope.launch {
            val user = userRepository.user.value
            user?.let {
                stepDataRepository.updateStepsForToday(steps, it.weight)
            }
        }
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Step Counter"
            val descriptionText = "Tracks your steps throughout the day"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        val notificationIntent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, notificationIntent,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Step2Energy")
            .setContentText("Tracking your steps: $currentSteps steps today")
            .setSmallIcon(R.drawable.ic_launcher_foreground) // Replace with appropriate icon
            .setContentIntent(pendingIntent)
            .build()
    }
    
    private fun updateNotification(steps: Int) {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Step2Energy")
            .setContentText("Tracking your steps: $steps steps today")
            .setSmallIcon(R.drawable.ic_launcher_foreground) // Replace with appropriate icon
            .build()
        
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
}
