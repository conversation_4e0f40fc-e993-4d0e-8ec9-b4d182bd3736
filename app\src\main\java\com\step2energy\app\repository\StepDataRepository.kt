package com.step2energy.app.repository

import androidx.lifecycle.LiveData
import com.step2energy.app.data.StepDataDao
import com.step2energy.app.model.StepData
import java.time.LocalDate
import java.time.format.DateTimeFormatter

class StepDataRepository(private val stepDataDao: StepDataDao) {
    
    private val dateFormatter = DateTimeFormatter.ISO_LOCAL_DATE
    
    fun getStepDataForToday(): LiveData<StepData?> {
        val today = LocalDate.now().format(dateFormatter)
        return stepDataDao.getStepDataForDate(today)
    }
    
    fun getAllStepData(): LiveData<List<StepData>> {
        return stepDataDao.getAllStepData()
    }
    
    fun getWeeklyStepData(): LiveData<List<StepData>> {
        val today = LocalDate.now()
        val weekAgo = today.minusDays(6)
        return stepDataDao.getStepDataBetweenDates(
            weekAgo.format(dateFormatter),
            today.format(dateFormatter)
        )
    }
    
    fun getMonthlyStepData(): LiveData<List<StepData>> {
        val today = LocalDate.now()
        val monthAgo = today.minusDays(29)
        return stepDataDao.getStepDataBetweenDates(
            monthAgo.format(dateFormatter),
            today.format(dateFormatter)
        )
    }
    
    fun getTotalStepsForWeek(): LiveData<Int?> {
        val today = LocalDate.now()
        val weekAgo = today.minusDays(6)
        return stepDataDao.getTotalStepsBetweenDates(
            weekAgo.format(dateFormatter),
            today.format(dateFormatter)
        )
    }
    
    fun getTotalStepsForMonth(): LiveData<Int?> {
        val today = LocalDate.now()
        val monthAgo = today.minusDays(29)
        return stepDataDao.getTotalStepsBetweenDates(
            monthAgo.format(dateFormatter),
            today.format(dateFormatter)
        )
    }
    
    fun getTotalCaloriesForWeek(): LiveData<Float?> {
        val today = LocalDate.now()
        val weekAgo = today.minusDays(6)
        return stepDataDao.getTotalCaloriesBetweenDates(
            weekAgo.format(dateFormatter),
            today.format(dateFormatter)
        )
    }
    
    fun getTotalCaloriesForMonth(): LiveData<Float?> {
        val today = LocalDate.now()
        val monthAgo = today.minusDays(29)
        return stepDataDao.getTotalCaloriesBetweenDates(
            monthAgo.format(dateFormatter),
            today.format(dateFormatter)
        )
    }
    
    fun getTotalJoulesForWeek(): LiveData<Float?> {
        val today = LocalDate.now()
        val weekAgo = today.minusDays(6)
        return stepDataDao.getTotalJoulesBetweenDates(
            weekAgo.format(dateFormatter),
            today.format(dateFormatter)
        )
    }
    
    fun getTotalJoulesForMonth(): LiveData<Float?> {
        val today = LocalDate.now()
        val monthAgo = today.minusDays(29)
        return stepDataDao.getTotalJoulesBetweenDates(
            monthAgo.format(dateFormatter),
            today.format(dateFormatter)
        )
    }
    
    suspend fun insert(stepData: StepData) {
        stepDataDao.insert(stepData)
    }
    
    suspend fun update(stepData: StepData) {
        stepDataDao.update(stepData)
    }
    
    suspend fun deleteAll() {
        stepDataDao.deleteAll()
    }
    
    suspend fun updateStepsForToday(steps: Int, userWeight: Float) {
        val today = LocalDate.now().format(dateFormatter)
        val existingData = stepDataDao.getStepDataForDate(today).value
        
        val calories = StepData.calculateCalories(steps, userWeight)
        val joules = StepData.convertCaloriesToJoules(calories)
        
        if (existingData != null) {
            val updatedData = existingData.copy(
                steps = steps,
                caloriesBurned = calories,
                joulesProduced = joules
            )
            stepDataDao.update(updatedData)
        } else {
            val newData = StepData(
                date = today,
                steps = steps,
                caloriesBurned = calories,
                joulesProduced = joules
            )
            stepDataDao.insert(newData)
        }
    }
}
