package com.step2energy.app.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.step2energy.app.Step2EnergyApp
import com.step2energy.app.databinding.FragmentEnergyBinding
import com.step2energy.app.viewmodel.StepDataViewModel
import java.text.NumberFormat
import java.util.*

class EnergyFragment : Fragment() {
    
    private var _binding: FragmentEnergyBinding? = null
    private val binding get() = _binding!!
    
    private val stepDataViewModel: StepDataViewModel by viewModels {
        val app = requireActivity().application as Step2EnergyApp
        StepDataViewModel.StepDataViewModelFactory(app.stepDataRepository, app.userRepository)
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentEnergyBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Observe weekly joules data
        stepDataViewModel.totalJoulesForWeek.observe(viewLifecycleOwner) { joules ->
            updateEnergyData(joules)
        }
    }
    
    private fun updateEnergyData(joules: Float) {
        // Format numbers with commas
        val numberFormat = NumberFormat.getNumberInstance(Locale.getDefault())
        
        // Update joules
        val joulesText = numberFormat.format(joules.toInt()) + " J"
        binding.totalJoulesValue.text = joulesText
        
        // Update kWh
        val kwhValue = stepDataViewModel.joulesToKWh(joules)
        val kwhText = String.format("%.3f kWh", kwhValue)
        binding.totalKwhValue.text = kwhText
        
        // Update device descriptions
        updateDeviceDescriptions(joules)
    }
    
    private fun updateDeviceDescriptions(joules: Float) {
        val deviceEquivalents = stepDataViewModel.getDevicePowerEquivalents(joules)
        
        for (device in deviceEquivalents) {
            val deviceName = device["device"] as String
            val value = device["value"] as Float
            
            when (deviceName) {
                "5W LED Bulb" -> {
                    val hours = value / 60
                    val description = if (hours < 1) {
                        String.format("Your energy could power a 5W LED bulb for %.1f minutes!", value)
                    } else {
                        String.format("Your energy could power a 5W LED bulb for %.1f hours!", hours)
                    }
                    binding.ledDescription.text = description
                }
                "Fan" -> {
                    val hours = value / 60
                    val description = if (hours < 1) {
                        String.format("Your energy could power a fan for %.1f minutes!", value)
                    } else {
                        String.format("Your energy could power a fan for %.1f hours!", hours)
                    }
                    binding.fanDescription.text = description
                }
                "Phone Charger" -> {
                    val description = String.format("Your energy could charge your phone from 0%% to %.1f%%!", value)
                    binding.phoneDescription.text = description
                }
            }
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
