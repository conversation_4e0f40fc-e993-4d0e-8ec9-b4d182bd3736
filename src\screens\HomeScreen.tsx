import React, { useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  ScrollView, 
  TouchableOpacity, 
  ActivityIndicator,
  Alert
} from 'react-native';
import { useStepData } from '../context/StepDataContext';
import { useUser } from '../context/UserContext';
import { formatNumberWithCommas } from '../utils/energyCalculations';
import { Ionicons } from '@expo/vector-icons';

const HomeScreen = () => {
  const { 
    todayStepData, 
    isAvailable, 
    isPedometerRunning, 
    startPedometer, 
    stopPedometer, 
    getDevicePowerEquivalents,
    isLoading 
  } = useStepData();
  
  const { user } = useUser();
  
  // Start pedometer when component mounts
  useEffect(() => {
    if (isAvailable && !isPedometerRunning && user) {
      startPedometer();
    }
    
    // Clean up when component unmounts
    return () => {
      if (isPedometerRunning) {
        stopPedometer();
      }
    };
  }, [isAvailable, isPedometerRunning, user]);
  
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
      </View>
    );
  }
  
  if (!isAvailable) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="warning-outline" size={48} color="#FF9800" />
        <Text style={styles.errorText}>
          Step counter is not available on this device.
        </Text>
      </View>
    );
  }
  
  if (!todayStepData) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>No step data available.</Text>
      </View>
    );
  }
  
  // Get device power equivalents
  const deviceEquivalents = getDevicePowerEquivalents(todayStepData.joulesProduced);
  
  // Find specific device data
  const ledData = deviceEquivalents.find(d => d.deviceName === '5W LED Bulb');
  const fanData = deviceEquivalents.find(d => d.deviceName === 'Fan');
  const phoneData = deviceEquivalents.find(d => d.deviceName === 'Phone Charge');
  
  return (
    <ScrollView style={styles.container}>
      {/* Steps Card */}
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Steps Today</Text>
        <Text style={styles.stepsCount}>
          {formatNumberWithCommas(todayStepData.steps)}
        </Text>
      </View>
      
      {/* Calories Card */}
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Calories Burned</Text>
        <Text style={styles.caloriesValue}>
          {todayStepData.caloriesBurned.toFixed(1)} kcal
        </Text>
      </View>
      
      {/* Energy Card */}
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Energy Produced</Text>
        <Text style={styles.joulesValue}>
          {formatNumberWithCommas(Math.round(todayStepData.joulesProduced))} J
        </Text>
        <Text style={styles.kwhValue}>
          {(todayStepData.joulesProduced / 3600000).toFixed(5)} kWh
        </Text>
      </View>
      
      {/* Devices Powered Card */}
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Devices You Could Power</Text>
        
        <View style={styles.deviceRow}>
          <Ionicons name="bulb-outline" size={24} color="#4CAF50" />
          <Text style={styles.deviceLabel}>5W LED Bulb</Text>
          <Text style={styles.deviceValue}>
            {ledData ? ledData.value.toFixed(1) : '0'} minutes
          </Text>
        </View>
        
        <View style={styles.deviceRow}>
          <Ionicons name="fan-outline" size={24} color="#4CAF50" />
          <Text style={styles.deviceLabel}>Fan</Text>
          <Text style={styles.deviceValue}>
            {fanData ? fanData.value.toFixed(1) : '0'} minutes
          </Text>
        </View>
        
        <View style={styles.deviceRow}>
          <Ionicons name="phone-portrait-outline" size={24} color="#4CAF50" />
          <Text style={styles.deviceLabel}>Phone Charge</Text>
          <Text style={styles.deviceValue}>
            {phoneData ? phoneData.value.toFixed(1) : '0'}%
          </Text>
        </View>
      </View>
      
      {/* Pedometer Status */}
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Pedometer status: {isPedometerRunning ? 'Running' : 'Stopped'}
        </Text>
        
        <TouchableOpacity
          style={[
            styles.statusButton,
            isPedometerRunning ? styles.stopButton : styles.startButton
          ]}
          onPress={() => {
            if (isPedometerRunning) {
              stopPedometer();
            } else {
              startPedometer();
            }
          }}
        >
          <Text style={styles.statusButtonText}>
            {isPedometerRunning ? 'Stop Tracking' : 'Start Tracking'}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#757575',
    textAlign: 'center',
    marginTop: 16,
  },
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  cardTitle: {
    fontSize: 16,
    color: '#757575',
    marginBottom: 8,
  },
  stepsCount: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  caloriesValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#8BC34A',
  },
  joulesValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#388E3C',
  },
  kwhValue: {
    fontSize: 16,
    color: '#757575',
    marginTop: 4,
  },
  deviceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  deviceLabel: {
    flex: 1,
    fontSize: 16,
    color: '#212121',
    marginLeft: 8,
  },
  deviceValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
  },
  statusContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 12,
  },
  statusButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 20,
    alignItems: 'center',
  },
  startButton: {
    backgroundColor: '#4CAF50',
  },
  stopButton: {
    backgroundColor: '#F44336',
  },
  statusButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default HomeScreen;
