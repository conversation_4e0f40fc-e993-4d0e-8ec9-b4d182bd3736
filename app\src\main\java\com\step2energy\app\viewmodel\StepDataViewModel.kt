package com.step2energy.app.viewmodel

import androidx.lifecycle.*
import com.step2energy.app.model.DevicePower
import com.step2energy.app.model.StepData
import com.step2energy.app.repository.StepDataRepository
import com.step2energy.app.repository.UserRepository
import kotlinx.coroutines.launch

class StepDataViewModel(
    private val stepDataRepository: StepDataRepository,
    private val userRepository: UserRepository
) : ViewModel() {
    
    // Step data for today
    val todayStepData: LiveData<StepData?> = stepDataRepository.getStepDataForToday()
    
    // Weekly step data
    val weeklyStepData: LiveData<List<StepData>> = stepDataRepository.getWeeklyStepData()
    
    // Monthly step data
    val monthlyStepData: LiveData<List<StepData>> = stepDataRepository.getMonthlyStepData()
    
    // Total steps for week
    val totalStepsForWeek: LiveData<Int> = Transformations.map(stepDataRepository.getTotalStepsForWeek()) {
        it ?: 0
    }
    
    // Total steps for month
    val totalStepsForMonth: LiveData<Int> = Transformations.map(stepDataRepository.getTotalStepsForMonth()) {
        it ?: 0
    }
    
    // Total calories for week
    val totalCaloriesForWeek: LiveData<Float> = Transformations.map(stepDataRepository.getTotalCaloriesForWeek()) {
        it ?: 0f
    }
    
    // Total calories for month
    val totalCaloriesForMonth: LiveData<Float> = Transformations.map(stepDataRepository.getTotalCaloriesForMonth()) {
        it ?: 0f
    }
    
    // Total joules for week
    val totalJoulesForWeek: LiveData<Float> = Transformations.map(stepDataRepository.getTotalJoulesForWeek()) {
        it ?: 0f
    }
    
    // Total joules for month
    val totalJoulesForMonth: LiveData<Float> = Transformations.map(stepDataRepository.getTotalJoulesForMonth()) {
        it ?: 0f
    }
    
    // Convert joules to kilowatt-hours
    fun joulesToKWh(joules: Float): Float {
        return joules / 3600000f // 1 kWh = 3,600,000 joules
    }
    
    // Get device power equivalents
    fun getDevicePowerEquivalents(joules: Float): List<Map<String, Any>> {
        val devices = DevicePower.getCommonDevices()
        val results = mutableListOf<Map<String, Any>>()
        
        for (device in devices) {
            when (device.deviceName) {
                "5W LED Bulb" -> {
                    val minutes = device.calculateDurationMinutes(joules)
                    results.add(mapOf(
                        "device" to device.deviceName,
                        "value" to minutes,
                        "unit" to "minutes"
                    ))
                }
                "Fan" -> {
                    val minutes = device.calculateDurationMinutes(joules)
                    results.add(mapOf(
                        "device" to device.deviceName,
                        "value" to minutes,
                        "unit" to "minutes"
                    ))
                }
                "Phone Charger" -> {
                    val percentage = device.calculatePhoneChargePercentage(joules)
                    results.add(mapOf(
                        "device" to device.deviceName,
                        "value" to percentage,
                        "unit" to "percent"
                    ))
                }
            }
        }
        
        return results
    }
    
    // Reset all step data
    fun resetAllStepData() {
        viewModelScope.launch {
            stepDataRepository.deleteAll()
        }
    }
    
    // Factory for creating StepDataViewModel with dependency injection
    class StepDataViewModelFactory(
        private val stepDataRepository: StepDataRepository,
        private val userRepository: UserRepository
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(StepDataViewModel::class.java)) {
                @Suppress("UNCHECKED_CAST")
                return StepDataViewModel(stepDataRepository, userRepository) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
}
