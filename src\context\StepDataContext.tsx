import React, { createContext, useState, useEffect, useContext } from 'react';
import * as SecureStore from 'expo-secure-store';
import { useUser } from './UserContext';
import { Pedometer } from 'expo-sensors';
import { calculateCalories, convertCaloriesToJoules, joulesToKWh } from '../utils/energyCalculations';

// Step data type definition
export interface StepData {
  date: string;
  steps: number;
  caloriesBurned: number;
  joulesProduced: number;
}

// Device power type definition
export interface DevicePower {
  deviceName: string;
  powerWatts: number;
  value: number;
  unit: string;
}

// Context type definition
interface StepDataContextType {
  todayStepData: StepData | null;
  weeklyStepData: StepData[];
  monthlyStepData: StepData[];
  isAvailable: boolean;
  isPedometerRunning: boolean;
  startPedometer: () => void;
  stopPedometer: () => void;
  resetAllStepData: () => Promise<void>;
  getDevicePowerEquivalents: (joules: number) => DevicePower[];
  isLoading: boolean;
}

// Create context
const StepDataContext = createContext<StepDataContextType | undefined>(undefined);

// Helper function to get today's date in ISO format
const getTodayDate = () => {
  const today = new Date();
  return today.toISOString().split('T')[0];
};

// Step data provider component
export const StepDataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useUser();
  const [todayStepData, setTodayStepData] = useState<StepData | null>(null);
  const [weeklyStepData, setWeeklyStepData] = useState<StepData[]>([]);
  const [monthlyStepData, setMonthlyStepData] = useState<StepData[]>([]);
  const [isAvailable, setIsAvailable] = useState(false);
  const [isPedometerRunning, setIsPedometerRunning] = useState(false);
  const [subscription, setSubscription] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check if pedometer is available
  useEffect(() => {
    const checkAvailability = async () => {
      const available = await Pedometer.isAvailableAsync();
      setIsAvailable(available);
    };

    checkAvailability();
  }, []);

  // Load step data on mount
  useEffect(() => {
    const loadStepData = async () => {
      try {
        // Load today's data
        const todayDate = getTodayDate();
        const todayDataStr = await SecureStore.getItemAsync(`step_data_${todayDate}`);

        if (todayDataStr) {
          setTodayStepData(JSON.parse(todayDataStr));
        } else {
          // Initialize today's data if it doesn't exist
          const initialData: StepData = {
            date: todayDate,
            steps: 0,
            caloriesBurned: 0,
            joulesProduced: 0
          };
          await SecureStore.setItemAsync(`step_data_${todayDate}`, JSON.stringify(initialData));
          setTodayStepData(initialData);
        }

        // Load weekly data (last 7 days)
        const weeklyData: StepData[] = [];
        for (let i = 0; i < 7; i++) {
          const date = new Date();
          date.setDate(date.getDate() - i);
          const dateStr = date.toISOString().split('T')[0];
          const dataStr = await SecureStore.getItemAsync(`step_data_${dateStr}`);

          if (dataStr) {
            weeklyData.push(JSON.parse(dataStr));
          }
        }
        setWeeklyStepData(weeklyData);

        // Load monthly data (last 30 days)
        const monthlyData: StepData[] = [];
        for (let i = 0; i < 30; i++) {
          const date = new Date();
          date.setDate(date.getDate() - i);
          const dateStr = date.toISOString().split('T')[0];
          const dataStr = await SecureStore.getItemAsync(`step_data_${dateStr}`);

          if (dataStr) {
            monthlyData.push(JSON.parse(dataStr));
          }
        }
        setMonthlyStepData(monthlyData);
      } catch (error) {
        console.error('Failed to load step data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadStepData();
  }, []);

  // Start pedometer
  const startPedometer = () => {
    if (!isAvailable || isPedometerRunning) return;

    const subscription = Pedometer.watchStepCount(result => {
      if (user && todayStepData) {
        // Add new steps to existing count
        const newSteps = todayStepData.steps + result.steps;
        const calories = calculateCalories(newSteps, user.weight);
        const joules = convertCaloriesToJoules(calories);

        const todayDate = getTodayDate();
        const updatedStepData: StepData = {
          date: todayDate,
          steps: newSteps,
          caloriesBurned: calories,
          joulesProduced: joules
        };

        // Update state
        setTodayStepData(updatedStepData);

        // Save to storage
        SecureStore.setItemAsync(`step_data_${todayDate}`, JSON.stringify(updatedStepData));
      }
    });

    setSubscription(subscription);
    setIsPedometerRunning(true);
  };

  // Stop pedometer
  const stopPedometer = () => {
    if (subscription) {
      subscription.remove();
      setSubscription(null);
    }
    setIsPedometerRunning(false);
  };

  // Reset all step data
  const resetAllStepData = async () => {
    try {
      // Get all keys
      const allKeys = await SecureStore.getItemAsync('all_step_data_keys');
      if (allKeys) {
        const keys = JSON.parse(allKeys) as string[];

        // Delete all step data
        for (const key of keys) {
          await SecureStore.deleteItemAsync(key);
        }

        // Reset keys
        await SecureStore.deleteItemAsync('all_step_data_keys');
      }

      // Reset state
      setTodayStepData(null);
      setWeeklyStepData([]);
      setMonthlyStepData([]);

      // Initialize today's data
      const todayDate = getTodayDate();
      const initialData: StepData = {
        date: todayDate,
        steps: 0,
        caloriesBurned: 0,
        joulesProduced: 0
      };
      await SecureStore.setItemAsync(`step_data_${todayDate}`, JSON.stringify(initialData));
      setTodayStepData(initialData);
    } catch (error) {
      console.error('Failed to reset step data:', error);
      throw error;
    }
  };

  // Get device power equivalents
  const getDevicePowerEquivalents = (joules: number): DevicePower[] => {
    // 5W LED Bulb
    const ledMinutes = joules / (5 * 60); // 5W = 5 joules per second

    // Fan (30W)
    const fanMinutes = joules / (30 * 60); // 30W = 30 joules per second

    // Phone Charge (assuming 10Wh battery = 36000 joules for full charge)
    const phoneChargePercent = (joules / 36000) * 100;

    return [
      {
        deviceName: '5W LED Bulb',
        powerWatts: 5,
        value: ledMinutes,
        unit: 'minutes'
      },
      {
        deviceName: 'Fan',
        powerWatts: 30,
        value: fanMinutes,
        unit: 'minutes'
      },
      {
        deviceName: 'Phone Charge',
        powerWatts: 5,
        value: phoneChargePercent,
        unit: 'percent'
      }
    ];
  };

  return (
    <StepDataContext.Provider
      value={{
        todayStepData,
        weeklyStepData,
        monthlyStepData,
        isAvailable,
        isPedometerRunning,
        startPedometer,
        stopPedometer,
        resetAllStepData,
        getDevicePowerEquivalents,
        isLoading
      }}
    >
      {children}
    </StepDataContext.Provider>
  );
};

// Custom hook to use the step data context
export const useStepData = () => {
  const context = useContext(StepDataContext);
  if (context === undefined) {
    throw new Error('useStepData must be used within a StepDataProvider');
  }
  return context;
};
